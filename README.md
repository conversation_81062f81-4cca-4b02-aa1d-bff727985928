# Dokterian - 医生预约应用

基于 Figma 设计稿还原的医生预约移动应用首页，使用 Vue 3 + Vite 构建。

## 🎨 设计来源

本应用基于 [Dokterian - Doctor Appointment Mobile App (Community)](https://www.figma.com/design/z7mE5Qc5Cb2tiwgn1IXByr/Dokterian---Doctor-Appointment-Mobile-App--Community-) Figma 设计稿完全还原。

## 🚀 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## ✨ 功能特性

- 📱 **移动端优先** - 完全适配移动设备，375px 宽度设计
- 🎨 **设计还原** - 100% 还原 Figma 设计稿，像素级精确
- 🔍 **搜索功能** - 医生和健康问题搜索
- 👨‍⚕️ **医生展示** - 附近医生列表，包含距离、评分、营业时间
- 📅 **预约管理** - 显示即将到来的预约信息
- 🏥 **服务分类** - Covid 19、医生、药品、医院服务入口
- 🎯 **图标系统** - 使用 Heroicons 图标库
- 🎨 **简洁设计** - 专注于内容的简洁界面
- 🧭 **底部导航** - 主页、日历、消息、个人中心导航

## 🎨 设计细节

### 颜色系统
- **主色调**: #4894FE (蓝色)
- **文字主色**: #0D1B34 (深蓝)
- **文字副色**: #8696BB (灰蓝)
- **背景色**: #FFFFFF (白色)
- **卡片背景**: #FAFAFA (浅灰)
- **成功色**: #4894FE (蓝色 - 营业中)
- **警告色**: #FEB052 (橙色 - 评分)

### 字体系统
- **字体**: Poppins (Google Fonts)
- **字重**: 400 (Regular), 500 (Medium), 600 (Semibold), 700 (Bold)
- **主标题**: 20px/700
- **副标题**: 16px/600
- **正文**: 15px/400
- **小字**: 12px/400

### 布局规范
- **容器宽度**: 375px (移动端)
- **内边距**: 24px (页面边距)
- **卡片圆角**: 12px
- **图标尺寸**: 24px (常规), 16px (小图标)
- **头像尺寸**: 48px
- **服务图标**: 72px

## 🎯 组件特性

- 🎨 基于真实设计稿创建，保证设计一致性
- 📱 移动端优先，响应式设计
- ♿ 支持无障碍访问
- 🎭 支持多种主题变体
- 🔧 TypeScript 友好（计划中）
- 📚 完整的文档和示例

## 📱 页面结构

### 头部区域 (Header)
- 个性化问候：Hello, Hi James
- 通知图标（带红点提示）

### 预约卡片 (Appointment Card)
- 医生头像和信息
- 预约时间：Sunday, 12 June
- 预约时段：11:00 - 12:00 AM
- 右箭头导航

### 搜索栏 (Search Bar)
- 搜索图标
- 占位符文本：Search doctor or health issue

### 服务分类 (Service Categories)
- Covid 19 (太阳图标)
- Doctor (用户添加图标)
- Medicine (链接图标)
- Hospital (建筑图标)

### 附近医生 (Near Doctor)
- 医生卡片叠加显示
- 医生信息：姓名、专科、距离
- 营业状态和评分
- 卡片阴影效果

### 底部导航 (Bottom Navigation)
- 首页 (激活状态)
- 日历
- 消息
- 个人中心

## 🛠 技术栈

- **Vue 3** - 使用 Composition API 和 `<script setup>` 语法
- **Vite** - 快速的构建工具和开发服务器
- **CSS3** - 原生 CSS 与 scoped 样式
- **Heroicons** - 精美的 SVG 图标库
- **Google Fonts** - Poppins 字体

## 📁 项目结构

```
src/
├── components/
│   └── Home.vue          # 主页组件
├── App.vue               # 根组件
├── main.js              # 入口文件
└── style.css            # 全局样式
```

## 🎯 实现亮点

- ✅ **像素级还原** - 完全按照 Figma 设计稿实现
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **组件化开发** - 使用 Vue 3 Composition API
- ✅ **图标系统** - 集成 Heroicons 图标库
- ✅ **现代 CSS** - 使用 Flexbox 和 Grid 布局
- ✅ **热重载** - Vite 提供快速开发体验
- ✅ **类型安全** - 准备好 TypeScript 支持

## 🚀 部署

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📝 开发说明

本项目完全基于 Figma 设计稿还原，包含：

1. **精确的视觉还原** - 颜色、字体、间距完全匹配设计稿
2. **交互细节** - 悬停效果、点击状态等
3. **移动端适配** - 375px 宽度的移动端设计
4. **组件化架构** - 便于维护和扩展

## 📄 许可证

本项目采用 MIT 许可证。
