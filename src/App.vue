<template>
  <div class="daccord-app">
    <!-- Left Sidebar -->
    <aside class="sidebar-left">
      <Sidebar />
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <MainContent />
    </main>

    <!-- Right Sidebar -->
    <aside class="sidebar-right">
      <RightSidebar />
    </aside>
  </div>
</template>

<script setup>
import Sidebar from './components/Sidebar.vue'
import MainContent from './components/MainContent.vue'
import RightSidebar from './components/RightSidebar.vue'
</script>

<style scoped>
.daccord-app {
  display: grid;
  grid-template-columns: var(--sidebar-width) var(--main-content-width) auto;
  width: var(--container-max-width);
  height: var(--app-height);
  margin: 0 auto;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.sidebar-left {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
}

.main-content {
  background: var(--bg-primary);
  overflow-y: auto;
}

.sidebar-right {
  width: 260px;
  background: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
}
</style>