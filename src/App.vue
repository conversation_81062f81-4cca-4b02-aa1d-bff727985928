<template>
  <div class="app">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="status-icons">
        <div class="signal"></div>
        <div class="wifi"></div>
        <div class="battery"></div>
      </div>
    </div>

    <!-- Header -->
    <div class="header">
      <div class="greeting">
        <div class="hello">Hello,</div>
        <div class="name">Hi <PERSON></div>
      </div>
      <div class="avatar">
        <img src="./assets/images/user-avatar.svg" alt="User Avatar" />
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Current Appointment Card -->
      <div class="appointment-card">
        <div class="appointment-header">
          <div class="doctor-info">
            <div class="doctor-avatar">
              <img src="./assets/images/doctor-avatar-1.png" alt="Dr. I<PERSON>ran <PERSON>" />
            </div>
            <div class="doctor-details">
              <h3>Dr. <PERSON><PERSON><PERSON></h3>
              <p>General Doctor</p>
            </div>
          </div>
          <div class="arrow-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M8.91 4.08L16.83 12L8.91 19.92" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        <div class="divider"></div>
        <div class="appointment-details">
          <div class="date-time">
            <div class="date">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M5.33 1.33V3.33M10.67 1.33V3.33M2.33 6.06H13.67M2 2.33H14V14.67H2V2.33Z" stroke="white" stroke-width="1.5"/>
                <circle cx="8.33" cy="9.47" r="0.33" fill="white"/>
                <circle cx="5.87" cy="9.47" r="0.33" fill="white"/>
                <circle cx="5.87" cy="11.47" r="0.33" fill="white"/>
              </svg>
              <span>Sunday, 12 June</span>
            </div>
            <div class="time-slot">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <circle cx="8" cy="8" r="6.67" stroke="white" stroke-width="1.5"/>
                <path d="M8 5.01V8L10.72 10.72" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>11:00 - 12:00 AM</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="search-bar">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <circle cx="11" cy="11" r="8" stroke="#8696BB" stroke-width="1.5"/>
          <path d="M21 21L16.65 16.65" stroke="#8696BB" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <input type="text" placeholder="Search doctor or health issue" />
      </div>

      <!-- Service Categories -->
      <div class="service-categories">
        <div class="category">
          <div class="category-icon covid">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="6.25" r="2.75" fill="#4894FE"/>
              <circle cx="12" cy="17.75" r="2.75" fill="#4894FE"/>
              <circle cx="6.23" cy="8.51" r="3.63" fill="#4894FE"/>
              <circle cx="17.77" cy="15.49" r="3.63" fill="#4894FE"/>
              <circle cx="17.77" cy="8.51" r="3.63" fill="#4894FE"/>
              <circle cx="6.23" cy="15.51" r="3.63" fill="#4894FE"/>
              <circle cx="12" cy="12" r="3.5" fill="#4894FE"/>
            </svg>
          </div>
          <span>Covid 19</span>
        </div>
        <div class="category">
          <div class="category-icon doctor">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12Z" fill="#4894FE"/>
              <path d="M12 14.5C6.99 14.5 2.91 17.86 2.91 22H21.09C21.09 17.86 17.01 14.5 12 14.5Z" fill="#4894FE"/>
            </svg>
          </div>
          <span>Doctor</span>
        </div>
        <div class="category">
          <div class="category-icon medicine">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M7.05 8.81L8.81 7.05L16.95 15.19L15.19 16.95L7.05 8.81Z" fill="#4894FE"/>
              <path d="M15.19 7.05L16.95 8.81L8.81 16.95L7.05 15.19L15.19 7.05Z" fill="#4894FE"/>
              <circle cx="12.36" cy="12.36" r="1.24" fill="#4894FE"/>
            </svg>
          </div>
          <span>Medicine</span>
        </div>
        <div class="category">
          <div class="category-icon hospital">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M1.25 21.25H22.75V22.75H1.25V21.25Z" fill="#4894FE"/>
              <path d="M3 2H21V21H3V2Z" fill="#4894FE"/>
            </svg>
          </div>
          <span>Hospital</span>
        </div>
      </div>

      <!-- Near Doctors Section -->
      <div class="near-doctors">
        <h2>Near Doctor</h2>
        <div class="doctors-list">
          <!-- Doctor 1 -->
          <div class="doctor-card">
            <div class="doctor-card-header">
              <div class="doctor-info">
                <div class="doctor-avatar">
                  <img src="./assets/images/doctor-avatar-1.png" alt="Dr. Imran Syahir" />
                </div>
                <div class="doctor-details">
                  <h3>Dr. Imran Syahir</h3>
                  <p>General Doctor</p>
                </div>
              </div>
              <div class="location">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M8 8.83C8.92 8.83 9.67 8.08 9.67 7.17C9.67 6.25 8.92 5.5 8 5.5C7.08 5.5 6.33 6.25 6.33 7.17C6.33 8.08 7.08 8.83 8 8.83Z" stroke="#8696BB" stroke-width="1.5"/>
                  <path d="M2.25 7.17C2.25 12.33 8 14.67 8 14.67C8 14.67 13.75 12.33 13.75 7.17C13.75 4.08 11.08 1.33 8 1.33C4.92 1.33 2.25 4.08 2.25 7.17Z" stroke="#8696BB" stroke-width="1.5"/>
                </svg>
                <span>1.2 KM</span>
              </div>
            </div>
            <div class="divider"></div>
            <div class="doctor-info-bottom">
              <div class="open-time">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <circle cx="10" cy="10" r="8.33" stroke="#4894FE" stroke-width="1.5"/>
                  <path d="M10 6.26V10L12.13 13.13" stroke="#4894FE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Open at 17.00</span>
              </div>
              <div class="rating">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <circle cx="10" cy="10" r="8.33" stroke="#FEB052" stroke-width="1.5"/>
                  <path d="M10 6.26V10L12.13 13.13" stroke="#FEB052" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>4,8 (120 Reviews)</span>
              </div>
            </div>
          </div>

          <!-- Doctor 2 -->
          <div class="doctor-card">
            <div class="doctor-card-header">
              <div class="doctor-info">
                <div class="doctor-avatar">
                  <img src="./assets/images/doctor-avatar-2.png" alt="Dr. Joseph Brostito" />
                </div>
                <div class="doctor-details">
                  <h3>Dr. Joseph Brostito</h3>
                  <p>Dental Specialist</p>
                </div>
              </div>
              <div class="location">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M8 8.83C8.92 8.83 9.67 8.08 9.67 7.17C9.67 6.25 8.92 5.5 8 5.5C7.08 5.5 6.33 6.25 6.33 7.17C6.33 8.08 7.08 8.83 8 8.83Z" stroke="#8696BB" stroke-width="1.5"/>
                  <path d="M2.25 7.17C2.25 12.33 8 14.67 8 14.67C8 14.67 13.75 12.33 13.75 7.17C13.75 4.08 11.08 1.33 8 1.33C4.92 1.33 2.25 4.08 2.25 7.17Z" stroke="#8696BB" stroke-width="1.5"/>
                </svg>
                <span>1.2 KM</span>
              </div>
            </div>
            <div class="divider"></div>
            <div class="doctor-info-bottom">
              <div class="open-time">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <circle cx="10" cy="10" r="8.33" stroke="#4894FE" stroke-width="1.5"/>
                  <path d="M10 6.26V10L12.13 13.13" stroke="#4894FE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Open at 17.00</span>
              </div>
              <div class="rating">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <circle cx="10" cy="10" r="8.33" stroke="#FEB052" stroke-width="1.5"/>
                  <path d="M10 6.26V10L12.13 13.13" stroke="#FEB052" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>4,8 (120 Reviews)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
      <div class="nav-item active">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M3.83 4H20.17L18 20H6L3.83 4Z" fill="#63B4FF"/>
          </svg>
        </div>
        <span>Home</span>
      </div>
      <div class="nav-item">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M8 2V5M16 2V5M3.5 9.09H20.5M3 3.5H21V20.5H3V3.5Z" stroke="#8696BB" stroke-width="1.5"/>
          </svg>
        </div>
      </div>
      <div class="nav-item">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M2 7L10.94 13.18C11.59 13.6 12.41 13.6 13.06 13.18L22 7" stroke="#8696BB" stroke-width="1.5"/>
          </svg>
        </div>
      </div>
      <div class="nav-item">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="7.44" r="4.44" stroke="#8696BB" stroke-width="1.5"/>
            <path d="M5.34 18.81C6.17 15.94 8.82 13.81 12 13.81C15.18 13.81 17.83 15.94 18.66 18.81" stroke="#8696BB" stroke-width="1.5"/>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>