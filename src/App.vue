<template>
  <div class="daccord-app">
    <!-- Left Sidebar -->
    <aside class="sidebar-left">
      <Sidebar />
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <MainContent />
    </main>

    <!-- Right Sidebar -->
    <aside class="sidebar-right">
      <RightSidebar />
    </aside>
  </div>
</template>

<script setup>
import Sidebar from './components/Sidebar.vue'
import MainContent from './components/MainContent.vue'
import RightSidebar from './components/RightSidebar.vue'
</script>

<style scoped>
.daccord-app {
  display: flex;
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
}

.sidebar-left {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  background: var(--bg-primary);
  overflow-y: auto;
}

.sidebar-right {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
  flex-shrink: 0;
}
</style>