<template>
  <div class="right-sidebar">
    <!-- Top Actions -->
    <div class="top-actions">
      <button class="action-btn">
        <CogIcon class="action-icon" />
      </button>
      <button class="action-btn">
        <ChatBubbleLeftIcon class="action-icon" />
      </button>
      <button class="action-btn">
        <BellIcon class="action-icon" />
      </button>
      <button class="action-btn">
        <EnvelopeIcon class="action-icon" />
      </button>
    </div>
    
    <!-- Activity Section -->
    <div class="activity-section">
      <div class="section-header">
        <h3>Activity</h3>
      </div>
      
      <div class="activity-list">
        <div class="activity-item">
          <div class="activity-avatar">
            <span class="avatar-text">A</span>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>Alex</strong> joined the community
            </div>
            <div class="activity-time">2 minutes ago</div>
          </div>
        </div>

        <div class="activity-item">
          <div class="activity-avatar">
            <span class="avatar-text">S</span>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>Sarah</strong> shared a new post
            </div>
            <div class="activity-time">5 minutes ago</div>
          </div>
        </div>

        <div class="activity-item">
          <div class="activity-avatar">
            <span class="avatar-text">M</span>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>Mike</strong> started a discussion
            </div>
            <div class="activity-time">10 minutes ago</div>
          </div>
        </div>

        <div class="activity-item">
          <div class="activity-avatar">
            <span class="avatar-text">E</span>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>Emma</strong> liked your post
            </div>
            <div class="activity-time">15 minutes ago</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- New Members Section -->
    <div class="members-section">
      <div class="section-header">
        <h3>New Members</h3>
      </div>
      
      <div class="members-list">
        <div class="member-item">
          <div class="member-avatar">
            <span class="avatar-text">JC</span>
          </div>
          <div class="member-info">
            <div class="member-name">Jessica Chen</div>
            <div class="member-status">Online</div>
          </div>
          <button class="add-friend-btn">
            <UserPlusIcon class="add-icon" />
          </button>
        </div>

        <div class="member-item">
          <div class="member-avatar">
            <span class="avatar-text">DK</span>
          </div>
          <div class="member-info">
            <div class="member-name">David Kim</div>
            <div class="member-status">Away</div>
          </div>
          <button class="add-friend-btn">
            <UserPlusIcon class="add-icon" />
          </button>
        </div>

        <div class="member-item">
          <div class="member-avatar">
            <span class="avatar-text">LW</span>
          </div>
          <div class="member-info">
            <div class="member-name">Lisa Wang</div>
            <div class="member-status">Online</div>
          </div>
          <button class="add-friend-btn">
            <UserPlusIcon class="add-icon" />
          </button>
        </div>
      </div>
    </div>
    
    <!-- Voice Channel Info -->
    <div class="voice-section">
      <div class="voice-info">
        <div class="voice-icon">
          <MicrophoneIcon class="voice-icon-svg" />
        </div>
        <div class="voice-text">
          <div class="voice-title">General Voice</div>
          <div class="voice-members">3 members</div>
        </div>
      </div>

      <div class="voice-controls">
        <button class="voice-btn">
          <SpeakerWaveIcon class="voice-control-icon" />
        </button>
        <button class="voice-btn">
          <SpeakerWaveIcon class="voice-control-icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  CogIcon,
  ChatBubbleLeftIcon,
  BellIcon,
  EnvelopeIcon,
  UserPlusIcon,
  MicrophoneIcon,
  SpeakerWaveIcon
} from '@heroicons/vue/24/outline'

// Note: HeadphonesIcon might not be available in heroicons, using SpeakerWaveIcon as alternative
</script>

<style scoped>
.right-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  gap: var(--spacing-xl);
}

/* Top Actions */
.top-actions {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-sm);
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 18px;
  height: 18px;
}

.action-btn:hover {
  background: var(--accent-blue);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* Section Headers */
.section-header {
  margin-bottom: var(--spacing-lg);
}

.section-header h3 {
  font-family: var(--font-secondary);
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Activity Section */
.activity-section {
  flex: 1;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.activity-item {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
}

.activity-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: var(--spacing-xs);
}

.activity-text strong {
  color: var(--text-primary);
}

.activity-time {
  font-size: 11px;
  color: var(--text-muted);
}

/* Members Section */
.members-section {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-xl);
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.member-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.member-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.member-status {
  font-size: 12px;
  color: var(--accent-blue);
}

.add-friend-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 14px;
  height: 14px;
}

.add-friend-btn:hover {
  background: var(--accent-blue);
  color: var(--text-primary);
}

/* Voice Section */
.voice-section {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.voice-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.voice-icon {
  width: 32px;
  height: 32px;
  background: var(--accent-blue);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-icon-svg {
  width: 16px;
  height: 16px;
  color: var(--text-primary);
}

.voice-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.voice-members {
  font-size: 12px;
  color: var(--text-muted);
}

.voice-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.voice-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-control-icon {
  width: 14px;
  height: 14px;
}

.voice-btn:hover {
  background: var(--accent-blue);
  color: var(--text-primary);
}
</style>
