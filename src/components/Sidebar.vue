<template>
  <div class="sidebar">
    <!-- Browser <PERSON>tons -->
    <div class="browser-buttons">
      <div class="browser-btn red"></div>
      <div class="browser-btn yellow"></div>
      <div class="browser-btn green"></div>
    </div>
    
    <!-- Logo -->
    <div class="logo-section">
      <div class="logo">
        <div class="logo-icon">
          <div class="logo-shape"></div>
        </div>
      </div>
    </div>
    
    <!-- Navigation Categories -->
    <div class="categories">
      <div class="category-header">
        <h3>Explore</h3>
      </div>
      
      <nav class="category-nav">
        <div class="nav-item active">
          <div class="nav-icon">🏠</div>
          <span>Home</span>
        </div>
        <div class="nav-item">
          <div class="nav-icon">🎵</div>
          <span>Music</span>
        </div>
        <div class="nav-item">
          <div class="nav-icon">🎮</div>
          <span>Gaming</span>
        </div>
        <div class="nav-item">
          <div class="nav-icon">📚</div>
          <span>Education</span>
        </div>
        <div class="nav-item">
          <div class="nav-icon">🔬</div>
          <span>Science & Tech</span>
        </div>
        <div class="nav-item">
          <div class="nav-icon">🎭</div>
          <span>Entertainment</span>
        </div>
        <div class="nav-item">
          <div class="nav-icon">🎓</div>
          <span>Student Hubs</span>
        </div>
      </nav>
    </div>
    
    <!-- User Section -->
    <div class="user-section">
      <div class="user-info">
        <div class="user-avatar">
          <img src="/src/assets/images/user-avatar.jpg" alt="User Avatar" />
        </div>
        <span class="username">sophiefortune</span>
      </div>
      
      <div class="user-actions">
        <button class="action-btn">🎤</button>
        <button class="action-btn">🔇</button>
        <button class="action-btn">➕</button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Component logic will be added here
</script>

<style scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
}

/* Browser Buttons */
.browser-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.browser-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.browser-btn.red {
  background: #ff5f57;
}

.browser-btn.yellow {
  background: #ffbd2e;
}

.browser-btn.green {
  background: #28ca42;
}

/* Logo Section */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-3xl);
}

.logo {
  width: 48px;
  height: 48px;
  position: relative;
}

.logo-icon {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-shape {
  width: 24px;
  height: 24px;
  background: var(--text-primary);
  border-radius: var(--radius-sm);
}

/* Categories */
.categories {
  flex: 1;
  margin-bottom: var(--spacing-xl);
}

.category-header {
  margin-bottom: var(--spacing-lg);
}

.category-header h3 {
  font-family: var(--font-secondary);
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.category-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.nav-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--accent-blue);
  color: var(--text-primary);
}

.nav-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.nav-item span {
  font-size: 14px;
  font-weight: 500;
}

/* User Section */
.user-section {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-lg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--accent-blue);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.user-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn:hover {
  background: var(--accent-blue);
  color: var(--text-primary);
}
</style>
