<template>
  <div class="sidebar">
    <!-- Browser Buttons -->
    <div class="browser-buttons">
      <div class="browser-btn red"></div>
      <div class="browser-btn yellow"></div>
      <div class="browser-btn green"></div>
    </div>

    <!-- Logo -->
    <div class="logo-section">
      <div class="logo">
        <div class="logo-icon">
          <div class="logo-shape">
            <div class="logo-inner"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Categories -->
    <div class="categories">
      <div class="category-header">
        <h3>Explore</h3>
      </div>

      <nav class="category-nav">
        <div class="nav-item active">
          <HomeIcon class="nav-icon" />
          <span>Home</span>
        </div>
        <div class="nav-item">
          <MusicalNoteIcon class="nav-icon" />
          <span>Music</span>
        </div>
        <div class="nav-item">
          <PuzzlePieceIcon class="nav-icon" />
          <span>Gaming</span>
        </div>
        <div class="nav-item">
          <BookOpenIcon class="nav-icon" />
          <span>Education</span>
        </div>
        <div class="nav-item">
          <BeakerIcon class="nav-icon" />
          <span>Science & Tech</span>
        </div>
        <div class="nav-item">
          <FilmIcon class="nav-icon" />
          <span>Entertainment</span>
        </div>
        <div class="nav-item">
          <AcademicCapIcon class="nav-icon" />
          <span>Student Hubs</span>
        </div>
      </nav>
    </div>

    <!-- User Section -->
    <div class="user-section">
      <div class="user-info">
        <div class="user-avatar">
          <span class="avatar-text">SF</span>
        </div>
        <span class="username">sophiefortune</span>
      </div>

      <div class="user-actions">
        <button class="action-btn">
          <MicrophoneIcon class="action-icon" />
        </button>
        <button class="action-btn">
          <SpeakerXMarkIcon class="action-icon" />
        </button>
        <button class="action-btn">
          <UserPlusIcon class="action-icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  HomeIcon,
  MusicalNoteIcon,
  PuzzlePieceIcon,
  BookOpenIcon,
  BeakerIcon,
  FilmIcon,
  AcademicCapIcon,
  MicrophoneIcon,
  SpeakerXMarkIcon,
  UserPlusIcon
} from '@heroicons/vue/24/outline'
</script>

<style scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 260px;
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
}

/* Browser Buttons */
.browser-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.browser-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.browser-btn.red {
  background: #ff5f57;
}

.browser-btn.yellow {
  background: #ffbd2e;
}

.browser-btn.green {
  background: #28ca42;
}

/* Logo Section */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-3xl);
}

.logo {
  width: 48px;
  height: 48px;
  position: relative;
}

.logo-icon {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-shape {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  border-radius: var(--radius-lg);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(91, 127, 214, 0.3);
}

.logo-inner {
  width: 16px;
  height: 16px;
  background: var(--text-primary);
  border-radius: 50%;
  opacity: 0.9;
}

/* Categories */
.categories {
  flex: 1;
  margin-bottom: var(--spacing-xl);
}

.category-header {
  margin-bottom: var(--spacing-lg);
}

.category-header h3 {
  font-family: var(--font-secondary);
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.category-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  color: var(--text-primary);
  box-shadow: 0 4px 12px rgba(91, 127, 214, 0.3);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-item span {
  font-size: 14px;
  font-weight: 500;
}

/* User Section */
.user-section {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-lg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--accent-blue);
}

.avatar-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.user-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 16px;
  height: 16px;
}

.action-btn:hover {
  background: var(--accent-blue);
  color: var(--text-primary);
}
</style>
