<template>
  <div class="main-content">
    <!-- Search Bar -->
    <div class="search-section">
      <div class="search-bar">
        <MagnifyingGlassIcon class="search-icon" />
        <input type="text" placeholder="Explore" class="search-input" />
      </div>
    </div>
    
    <!-- Content Container -->
    <div class="content-container">
      <!-- Banner -->
      <div class="banner">
        <div class="banner-content">
          <h1 class="banner-title">
            Find Your Community<br />
            on Daccord
          </h1>
        </div>
        <div class="banner-image">
          <!-- Banner background will be added via CSS -->
        </div>
      </div>
      
      <!-- Featured Community Section -->
      <section class="section">
        <div class="section-header">
          <h2>Featured Community</h2>
          <a href="#" class="see-all">See all</a>
        </div>
        
        <div class="cards-grid">
          <div class="community-card featured">
            <div class="card-image">
              <div class="placeholder-image">
                <PaintBrushIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Design Community</h3>
              <p>Creative designers sharing ideas</p>
              <div class="card-stats">
                <span>1.2k members</span>
                <span>Online</span>
              </div>
            </div>
          </div>

          <div class="community-card featured">
            <div class="card-image">
              <div class="placeholder-image">
                <BeakerIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Tech Hub</h3>
              <p>Developers and tech enthusiasts</p>
              <div class="card-stats">
                <span>2.5k members</span>
                <span>Online</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <!-- Popular Section -->
      <section class="section">
        <div class="section-header">
          <h2>Popular</h2>
          <a href="#" class="see-all">See all</a>
        </div>
        
        <div class="cards-grid">
          <div class="community-card">
            <div class="card-image">
              <div class="placeholder-image">
                <PuzzlePieceIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Gaming Central</h3>
              <p>Gamers unite and play together</p>
              <div class="card-stats">
                <span>5.1k members</span>
                <span>Online</span>
              </div>
            </div>
          </div>

          <div class="community-card">
            <div class="card-image">
              <div class="placeholder-image">
                <MusicalNoteIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Music Lovers</h3>
              <p>Share and discover new music</p>
              <div class="card-stats">
                <span>3.8k members</span>
                <span>Online</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <!-- Recent Section -->
      <section class="section">
        <div class="section-header">
          <h2>Recent</h2>
          <a href="#" class="see-all">See all</a>
        </div>
        
        <div class="cards-grid">
          <div class="community-card">
            <div class="card-image">
              <div class="placeholder-image">
                <BookOpenIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Study Group</h3>
              <p>Students helping each other</p>
              <div class="card-stats">
                <span>892 members</span>
                <span>Online</span>
              </div>
            </div>
          </div>

          <div class="community-card">
            <div class="card-image">
              <div class="placeholder-image">
                <PaintBrushIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Art Gallery</h3>
              <p>Artists showcasing their work</p>
              <div class="card-stats">
                <span>1.5k members</span>
                <span>Online</span>
              </div>
            </div>
          </div>

          <div class="community-card">
            <div class="card-image">
              <div class="placeholder-image">
                <BeakerIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="card-content">
              <h3>Science Lab</h3>
              <p>Researchers and science enthusiasts</p>
              <div class="card-stats">
                <span>967 members</span>
                <span>Online</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import {
  MagnifyingGlassIcon,
  PuzzlePieceIcon,
  MusicalNoteIcon,
  BookOpenIcon,
  PaintBrushIcon,
  BeakerIcon
} from '@heroicons/vue/24/outline'
</script>

<style scoped>
.main-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 1140px;
  overflow-y: auto;
  background: var(--bg-primary);
}

/* Search Section */
.search-section {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.search-bar {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  max-width: 400px;
  transition: all 0.3s ease;
}

.search-bar:focus-within {
  border-color: var(--accent-blue);
  box-shadow: 0 4px 20px rgba(91, 127, 214, 0.2);
  transform: translateY(-1px);
}

.search-icon {
  width: 16px;
  height: 16px;
  color: var(--text-muted);
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

/* Content Container */
.content-container {
  padding: var(--spacing-2xl);
}

/* Banner */
.banner {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));
  border-radius: var(--radius-2xl);
  padding: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
  position: relative;
  overflow: hidden;
  min-height: 200px;
  display: flex;
  align-items: center;
}

.banner-title {
  font-family: var(--font-secondary);
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
  z-index: 2;
  position: relative;
}

/* Sections */
.section {
  margin-bottom: var(--spacing-3xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.section-header h2 {
  font-family: var(--font-secondary);
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.see-all {
  color: var(--accent-blue);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.see-all:hover {
  color: var(--accent-purple);
}

/* Cards Grid */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.community-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--border-color);
  position: relative;
}

.community-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(91, 127, 214, 0.1), rgba(135, 108, 245, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.community-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  border-color: var(--accent-blue);
}

.community-card:hover::before {
  opacity: 1;
}

.card-image {
  height: 150px;
  overflow: hidden;
  position: relative;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  width: 48px;
  height: 48px;
  color: var(--text-primary);
}

.card-content {
  padding: var(--spacing-lg);
  position: relative;
  z-index: 2;
}

.card-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.card-content p {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.card-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-muted);
}

.featured {
  border: 2px solid transparent;
  background: linear-gradient(var(--bg-card), var(--bg-card)) padding-box,
              linear-gradient(135deg, var(--accent-pink), var(--accent-blue)) border-box;
}
</style>
