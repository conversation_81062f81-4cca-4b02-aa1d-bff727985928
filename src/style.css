@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #FFFFFF;
  overflow-x: hidden;
}

.app {
  max-width: 375px;
  margin: 0 auto;
  min-height: 100vh;
  background-color: #FFFFFF;
  position: relative;
}

/* Status Bar */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  height: 44px;
  background-color: #FFFFFF;
}

.status-bar .time {
  font-family: 'SF UI Text', sans-serif;
  font-weight: 600;
  font-size: 15px;
  color: #000000;
  letter-spacing: -0.5px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal, .wifi, .battery {
  width: 17px;
  height: 11px;
  background-color: #0D1B34;
  border-radius: 2px;
}

.wifi {
  width: 15px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%230D1B34' viewBox='0 0 24 24'%3E%3Cpath d='M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l2 2 2-2c-2.76-2.76-7.24-2.76-10 0z'/%3E%3C/svg%3E") no-repeat center;
}

.signal {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%230D1B34' viewBox='0 0 24 24'%3E%3Cpath d='M2 17h20v2H2zm1.15-4.05L4 11.47l.85 1.48L3 12.95zm3.17-5.52L7.17 9.9l1.48-.85L7.8 7.58zm3.23-2.97L10.4 6.93 12 6.08l-1.6-.85zM16 4l1.45 2.5L16 7.5V4z'/%3E%3C/svg%3E") no-repeat center;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  margin-bottom: 32px;
}

.greeting {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.greeting .hello {
  font-weight: 400;
  font-size: 16px;
  line-height: 1.2;
  color: #8696BB;
}

.greeting .name {
  font-weight: 700;
  font-size: 20px;
  line-height: 1.1;
  color: #0D1B34;
}

.avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Main Content */
.main-content {
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 100px;
}

/* Appointment Card */
.appointment-card {
  background: #4894FE;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 327px;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.doctor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-details h3 {
  font-weight: 700;
  font-size: 16px;
  line-height: 1.1;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.doctor-details p {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
  color: #CBE1FF;
}

.arrow-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.divider {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.15);
}

.appointment-details {
  width: 100%;
}

.date-time {
  display: flex;
  gap: 12px;
}

.date, .time-slot {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.date span, .time-slot span {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  color: #FFFFFF;
}

/* Search Bar */
.search-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #FAFAFA;
  border-radius: 12px;
  width: 327px;
}

.search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.5;
  color: #0D1B34;
  outline: none;
}

.search-bar input::placeholder {
  color: #8696BB;
}

/* Service Categories */
.service-categories {
  display: flex;
  gap: 13px;
  justify-content: space-between;
}

.category {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.category-icon {
  width: 72px;
  height: 72px;
  background: #FAFAFA;
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.category span {
  font-weight: 400;
  font-size: 15px;
  line-height: 1.5;
  color: #8696BB;
  text-align: center;
}

/* Near Doctors */
.near-doctors {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.near-doctors h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 1.1;
  color: #0D1B34;
}

.doctors-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  width: 327px;
  height: 308px;
}

/* Doctor Cards */
.doctor-card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  box-shadow: 2px 12px 20px 0px rgba(90, 117, 167, 0.04);
  position: absolute;
  width: 295px;
  left: 0;
}

.doctor-card:first-child {
  top: 160px;
  z-index: 1;
}

.doctor-card:last-child {
  top: 0;
  z-index: 2;
}

.doctor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 63px;
}

.doctor-card .doctor-info {
  display: flex;
  gap: 10px;
}

.doctor-card .doctor-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.doctor-card .doctor-details h3 {
  font-weight: 700;
  font-size: 16px;
  line-height: 1.1;
  color: #0D1B34;
  margin-bottom: 0;
}

.doctor-card .doctor-details p {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
  color: #8696BB;
}

.location {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location span {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
  color: #8696BB;
}

.doctor-card .divider {
  background: #F5F5F5;
}

.doctor-info-bottom {
  display: flex;
  width: 100%;
  gap: 12px;
}

.open-time, .rating {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.open-time span {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  color: #4894FE;
}

.rating span {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.5;
  color: #FEB052;
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 375px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #FFFFFF;
  border-top: 1px solid rgba(246, 246, 246, 1);
  box-shadow: inset 0px 1px 0px -7px rgba(246, 246, 246, 1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  flex: 1;
  cursor: pointer;
}

.nav-item.active {
  background: rgba(99, 180, 255, 0.1);
  border-radius: 12px;
}

.nav-item.active span {
  font-weight: 700;
  font-size: 14px;
  line-height: 1.36;
  color: #63B4FF;
}

.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 375px) {
  .app {
    max-width: 100vw;
  }
  
  .bottom-nav {
    width: 100vw;
  }
  
  .appointment-card,
  .search-bar,
  .doctors-list {
    width: calc(100vw - 48px);
  }
}

/* Animations */
.doctor-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.doctor-card:hover {
  transform: translateY(-2px);
  box-shadow: 2px 16px 24px 0px rgba(90, 117, 167, 0.08);
}

.nav-item {
  transition: background-color 0.2s ease;
}

.category-icon {
  transition: transform 0.2s ease;
}

.category-icon:hover {
  transform: scale(1.05);
}