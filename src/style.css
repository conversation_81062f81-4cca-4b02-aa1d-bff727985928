/* Daccord App Global Styles */
:root {
  /* Color Variables */
  --bg-primary: #1a1d29;
  --bg-secondary: #252837;
  --bg-tertiary: #2f3349;
  --bg-card: #393d5e;
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --accent-blue: #5b7fd6;
  --accent-purple: #876cf5;
  --accent-pink: #f449f4;
  --accent-cyan: #1bb1d0;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.25);

  /* Font Variables */
  --font-primary: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-secondary: 'SF Compact Rounded', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, sans-serif;
  --font-tertiary: 'Lato', sans-serif;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;

  /* Layout */
  --sidebar-width: 260px;
  --main-content-width: 1140px;
  --container-max-width: 1400px;
  --app-height: 1036px;
  --header-height: 60px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  font-weight: 400;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  width: 100vw;
  height: 100vh;
  display: flex;
  overflow: hidden;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue);
}

/* Utility Classes */
.gradient-border {
  position: relative;
  background: linear-gradient(135deg, var(--accent-pink), var(--accent-blue), var(--accent-purple));
  border-radius: var(--radius-xl);
  padding: 2px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: var(--bg-secondary);
  border-radius: calc(var(--radius-xl) - 2px);
  z-index: -1;
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
}

.card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 10px var(--shadow-color);
}

.btn {
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn:hover {
  background: var(--accent-blue);
  transform: translateY(-1px);
}

.text-gradient {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
